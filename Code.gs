/**
 * ERP System - Main Entry Point
 * Professional Dynamic ERP System using Google Apps Script & Google Sheets
 * Architecture: MVC Pattern with Dynamic Module Loading
 * Author: ERP System Generator
 * Version: 1.0.0
 */

// Global Configuration
const ERP_CONFIG = {
  SYSTEM_NAME: 'Dynamic ERP System',
  VERSION: '1.0.0',
  SPREADSHEET_PREFIX: 'ERP_',
  DEFAULT_PERMISSIONS: {
    READ: true,
    write: true,
    delete: true,
    admin: true
  },
  MODULES: [
    'CRM', 'Sales', 'Purchases', 'Products', 'Inventory', 
    'Manufacturing', 'Treasury', 'Wallets', 'Accounting', 
    'VanDistribution', 'Settings'
  ]
};

/**
 * Main entry point for the web application
 */
function doGet(e) {
  try {
    // Initialize system if needed
    initializeSystem();
    
    // Create and return the main HTML output
    const template = HtmlService.createTemplateFromFile('index');
    template.config = ERP_CONFIG;
    
    const htmlOutput = template.evaluate()
      .setTitle(ERP_CONFIG.SYSTEM_NAME)
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
    
    return htmlOutput;
  } catch (error) {
    Logger.log('Error in doGet: ' + error.toString());
    return HtmlService.createHtmlOutput('<h1>System Error</h1><p>' + error.toString() + '</p>');
  }
}

/**
 * Include function for HTML templates
 */
function include(filename) {
  try {
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    Logger.log('Error including file ' + filename + ': ' + error.toString());
    return '<!-- Error loading ' + filename + ' -->';
  }
}

/**
 * Initialize the ERP system
 */
function initializeSystem() {
  try {
    // Check if system is already initialized
    const properties = PropertiesService.getScriptProperties();
    const isInitialized = properties.getProperty('ERP_INITIALIZED');
    
    if (!isInitialized) {
      Logger.log('Initializing ERP System...');
      
      // Create system user with full permissions
      createSystemUser();
      
      // Mark system as initialized
      properties.setProperty('ERP_INITIALIZED', 'true');
      properties.setProperty('ERP_INIT_DATE', new Date().toISOString());
      
      Logger.log('ERP System initialized successfully');
    }
  } catch (error) {
    Logger.log('Error initializing system: ' + error.toString());
    throw error;
  }
}

/**
 * Create system user with full permissions
 */
function createSystemUser() {
  try {
    const userEmail = Session.getActiveUser().getEmail();
    const userPermissions = {
      email: userEmail,
      name: 'System Administrator',
      role: 'ADMIN',
      permissions: ERP_CONFIG.DEFAULT_PERMISSIONS,
      createdAt: new Date().toISOString(),
      isActive: true
    };
    
    // Store user permissions
    const properties = PropertiesService.getScriptProperties();
    properties.setProperty('USER_' + userEmail, JSON.stringify(userPermissions));
    
    Logger.log('System user created: ' + userEmail);
  } catch (error) {
    Logger.log('Error creating system user: ' + error.toString());
    throw error;
  }
}

/**
 * Get current user permissions
 */
function getCurrentUserPermissions() {
  try {
    const userEmail = Session.getActiveUser().getEmail();
    const properties = PropertiesService.getScriptProperties();
    const userDataStr = properties.getProperty('USER_' + userEmail);
    
    if (userDataStr) {
      return JSON.parse(userDataStr);
    }
    
    // Return default limited permissions for unknown users
    return {
      email: userEmail,
      name: 'Guest User',
      role: 'GUEST',
      permissions: {
        read: true,
        write: false,
        delete: false,
        admin: false
      },
      isActive: false
    };
  } catch (error) {
    Logger.log('Error getting user permissions: ' + error.toString());
    return null;
  }
}

/**
 * Test system connection
 */
function testConnection() {
  try {
    const user = getCurrentUserPermissions();
    return {
      success: true,
      message: 'Connection successful',
      user: user,
      timestamp: new Date().toISOString(),
      systemInfo: {
        name: ERP_CONFIG.SYSTEM_NAME,
        version: ERP_CONFIG.VERSION
      }
    };
  } catch (error) {
    return {
      success: false,
      message: 'Connection failed: ' + error.toString(),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Dynamic module loader
 */
function loadModule(moduleName) {
  try {
    const user = getCurrentUserPermissions();
    if (!user || !user.permissions.read) {
      throw new Error('Access denied');
    }
    
    // Validate module name
    if (!ERP_CONFIG.MODULES.includes(moduleName)) {
      throw new Error('Invalid module: ' + moduleName);
    }
    
    // Load module view
    const viewContent = include(moduleName.toLowerCase() + '_view');
    
    return {
      success: true,
      moduleName: moduleName,
      content: viewContent,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    Logger.log('Error loading module ' + moduleName + ': ' + error.toString());
    return {
      success: false,
      message: 'Failed to load module: ' + error.toString(),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get dashboard data
 */
function getDashboardData() {
  try {
    const user = getCurrentUserPermissions();
    if (!user || !user.permissions.read) {
      throw new Error('Access denied');
    }
    
    return {
      success: true,
      user: user,
      modules: ERP_CONFIG.MODULES,
      systemInfo: {
        name: ERP_CONFIG.SYSTEM_NAME,
        version: ERP_CONFIG.VERSION,
        initDate: PropertiesService.getScriptProperties().getProperty('ERP_INIT_DATE')
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    Logger.log('Error getting dashboard data: ' + error.toString());
    return {
      success: false,
      message: 'Failed to get dashboard data: ' + error.toString(),
      timestamp: new Date().toISOString()
    };
  }
}
