<style>
/* Custom ERP System Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1f2937;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
}

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Loading Screen Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Sidebar Styles */
#sidebar {
    transition: width 0.3s ease;
}

#sidebar.collapsed {
    width: 4rem;
}

#sidebar.collapsed .sidebar-text {
    display: none;
}

#sidebar.collapsed .module-group h3 {
    display: none;
}

/* Module Buttons */
.module-btn {
    transition: all 0.3s ease;
    border: none;
    background: transparent;
}

.module-btn:hover {
    background-color: rgba(55, 65, 81, 0.7) !important;
    transform: translateX(-5px);
}

.module-btn.active {
    background-color: var(--primary-color) !important;
}

/* Content Area */
#contentArea {
    min-height: calc(100vh - 120px);
    animation: fadeIn 0.5s ease;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Tables */
.table {
    border-radius: 12px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
}

/* Dashboard Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.stats-card.success {
    background: linear-gradient(135deg, var(--success-color), #34d399);
}

.stats-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.stats-card.danger {
    background: linear-gradient(135deg, var(--danger-color), #f87171);
}

/* Modal Styles */
.modal-content {
    border-radius: 16px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 16px 16px;
}

/* Alerts */
.alert {
    border-radius: 12px;
    border: none;
    animation: slideInRight 0.5s ease;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    #sidebar {
        position: fixed;
        z-index: 1000;
        height: 100vh;
        transform: translateX(-100%);
    }
    
    #sidebar.show {
        transform: translateX(0);
    }
    
    .container-fluid {
        padding-left: 0;
    }
    
    #sidebarToggle {
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem;
    }
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* Map Containers */
.map-container {
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-custom {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

.animate-slide-in-right {
    animation: slideInRight 0.5s ease;
}

.animate-slide-in-left {
    animation: slideInLeft 0.5s ease;
}

.animate-pulse-custom {
    animation: pulse 2s infinite;
}

/* Print Styles */
@media print {
    #sidebar, nav, .btn, .no-print {
        display: none !important;
    }
    
    #contentArea {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
</style>
