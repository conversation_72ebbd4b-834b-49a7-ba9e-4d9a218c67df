/**
 * Products Controller - Product Management
 * Handles all product operations and inventory tracking
 */

class ProductsController {
  constructor() {
    this.moduleName = 'Products';
    this.schema = {
      id: { type: 'string', width: 120 },
      productCode: { type: 'string', width: 120 },
      name: { type: 'string', width: 200 },
      description: { type: 'string', width: 300 },
      category: { type: 'string', width: 150 },
      brand: { type: 'string', width: 150 },
      unit: { type: 'string', width: 100 },
      costPrice: { type: 'number', width: 120 },
      sellingPrice: { type: 'number', width: 120 },
      wholesalePrice: { type: 'number', width: 120 },
      minStock: { type: 'number', width: 100 },
      maxStock: { type: 'number', width: 100 },
      currentStock: { type: 'number', width: 120 },
      reorderLevel: { type: 'number', width: 120 },
      barcode: { type: 'string', width: 150 },
      sku: { type: 'string', width: 120 },
      weight: { type: 'number', width: 100 },
      dimensions: { type: 'string', width: 150 },
      supplier: { type: 'string', width: 150 },
      taxRate: { type: 'number', width: 100 },
      isActive: { type: 'boolean', width: 80 },
      isTrackable: { type: 'boolean', width: 80 },
      notes: { type: 'string', width: 300 },
      createdAt: { type: 'date', width: 150 },
      updatedAt: { type: 'date', width: 150 }
    };
    
    this.initializeModule();
  }

  /**
   * Initialize Products module
   */
  initializeModule() {
    try {
      DB.initializeSchema(this.moduleName, this.schema);
      Logger.log('Products module initialized successfully');
    } catch (error) {
      Logger.log('Error initializing Products module: ' + error.toString());
      throw error;
    }
  }

  /**
   * Create a new product
   */
  createProduct(productData) {
    try {
      // Validate required fields
      if (!productData.name) {
        throw new Error('اسم المنتج مطلوب');
      }

      // Generate product code if not provided
      if (!productData.productCode) {
        productData.productCode = this.generateProductCode();
      }

      // Check if product code already exists
      const existingProduct = DB.find(this.moduleName, { productCode: productData.productCode });
      if (existingProduct.length > 0) {
        throw new Error('كود المنتج موجود مسبقاً');
      }

      // Set default values
      productData.unit = productData.unit || 'قطعة';
      productData.costPrice = productData.costPrice || 0;
      productData.sellingPrice = productData.sellingPrice || 0;
      productData.wholesalePrice = productData.wholesalePrice || 0;
      productData.currentStock = productData.currentStock || 0;
      productData.minStock = productData.minStock || 0;
      productData.maxStock = productData.maxStock || 0;
      productData.reorderLevel = productData.reorderLevel || 0;
      productData.taxRate = productData.taxRate || 15;
      productData.isActive = productData.isActive !== false;
      productData.isTrackable = productData.isTrackable !== false;

      // Insert product
      const product = DB.insert(this.moduleName, productData);
      
      Logger.log('Product created: ' + product.id);
      return {
        success: true,
        data: product,
        message: 'تم إنشاء المنتج بنجاح'
      };
    } catch (error) {
      Logger.log('Error creating product: ' + error.toString());
      return {
        success: false,
        message: 'فشل في إنشاء المنتج: ' + error.toString()
      };
    }
  }

  /**
   * Get all products
   */
  getAllProducts(filters = {}) {
    try {
      let products = DB.find(this.moduleName, filters);
      
      // Sort by creation date (newest first)
      products.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      return {
        success: true,
        data: products,
        count: products.length
      };
    } catch (error) {
      Logger.log('Error getting products: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب المنتجات: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get product by ID
   */
  getProductById(productId) {
    try {
      const product = DB.findById(this.moduleName, productId);
      
      if (!product) {
        return {
          success: false,
          message: 'المنتج غير موجود'
        };
      }

      return {
        success: true,
        data: product
      };
    } catch (error) {
      Logger.log('Error getting product by ID: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب بيانات المنتج: ' + error.toString()
      };
    }
  }

  /**
   * Update product
   */
  updateProduct(productId, updateData) {
    try {
      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.createdAt;

      // Check if product code is being changed and if it already exists
      if (updateData.productCode) {
        const existingProduct = DB.find(this.moduleName, { productCode: updateData.productCode });
        if (existingProduct.length > 0 && existingProduct[0].id !== productId) {
          throw new Error('كود المنتج موجود مسبقاً');
        }
      }

      const updatedProduct = DB.update(this.moduleName, productId, updateData);
      
      if (!updatedProduct) {
        return {
          success: false,
          message: 'المنتج غير موجود'
        };
      }

      Logger.log('Product updated: ' + productId);
      return {
        success: true,
        data: updatedProduct,
        message: 'تم تحديث المنتج بنجاح'
      };
    } catch (error) {
      Logger.log('Error updating product: ' + error.toString());
      return {
        success: false,
        message: 'فشل في تحديث المنتج: ' + error.toString()
      };
    }
  }

  /**
   * Delete product
   */
  deleteProduct(productId) {
    try {
      const deleted = DB.delete(this.moduleName, productId);
      
      if (!deleted) {
        return {
          success: false,
          message: 'المنتج غير موجود'
        };
      }

      Logger.log('Product deleted: ' + productId);
      return {
        success: true,
        message: 'تم حذف المنتج بنجاح'
      };
    } catch (error) {
      Logger.log('Error deleting product: ' + error.toString());
      return {
        success: false,
        message: 'فشل في حذف المنتج: ' + error.toString()
      };
    }
  }

  /**
   * Search products
   */
  searchProducts(searchTerm) {
    try {
      const allProducts = DB.find(this.moduleName);
      
      if (!searchTerm) {
        return {
          success: true,
          data: allProducts,
          count: allProducts.length
        };
      }

      const searchResults = allProducts.filter(product => {
        const searchFields = [
          product.name,
          product.productCode,
          product.description,
          product.category,
          product.brand,
          product.barcode,
          product.sku
        ];
        
        return searchFields.some(field => 
          field && field.toString().toLowerCase().includes(searchTerm.toLowerCase())
        );
      });

      return {
        success: true,
        data: searchResults,
        count: searchResults.length
      };
    } catch (error) {
      Logger.log('Error searching products: ' + error.toString());
      return {
        success: false,
        message: 'فشل في البحث عن المنتجات: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get product statistics
   */
  getProductStats() {
    try {
      const allProducts = DB.find(this.moduleName);
      
      const stats = {
        totalProducts: allProducts.length,
        activeProducts: allProducts.filter(p => p.isActive).length,
        inactiveProducts: allProducts.filter(p => !p.isActive).length,
        lowStockProducts: allProducts.filter(p => p.currentStock <= p.reorderLevel).length,
        outOfStockProducts: allProducts.filter(p => p.currentStock <= 0).length,
        totalStockValue: allProducts.reduce((sum, p) => sum + ((parseFloat(p.currentStock) || 0) * (parseFloat(p.costPrice) || 0)), 0),
        totalSellingValue: allProducts.reduce((sum, p) => sum + ((parseFloat(p.currentStock) || 0) * (parseFloat(p.sellingPrice) || 0)), 0),
        productsByCategory: {}
      };

      // Group by category
      allProducts.forEach(product => {
        const category = product.category || 'غير مصنف';
        stats.productsByCategory[category] = (stats.productsByCategory[category] || 0) + 1;
      });

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      Logger.log('Error getting product stats: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب إحصائيات المنتجات: ' + error.toString()
      };
    }
  }

  /**
   * Update product stock
   */
  updateProductStock(productId, quantity, operation = 'add') {
    try {
      const product = DB.findById(this.moduleName, productId);
      
      if (!product) {
        return {
          success: false,
          message: 'المنتج غير موجود'
        };
      }

      let newStock = parseFloat(product.currentStock) || 0;
      
      if (operation === 'add') {
        newStock += parseFloat(quantity);
      } else if (operation === 'subtract') {
        newStock -= parseFloat(quantity);
      } else if (operation === 'set') {
        newStock = parseFloat(quantity);
      }

      // Ensure stock doesn't go negative
      if (newStock < 0) {
        newStock = 0;
      }

      const updatedProduct = DB.update(this.moduleName, productId, { currentStock: newStock });

      Logger.log(`Product stock updated: ${productId}, new stock: ${newStock}`);
      return {
        success: true,
        data: updatedProduct,
        message: 'تم تحديث المخزون بنجاح'
      };
    } catch (error) {
      Logger.log('Error updating product stock: ' + error.toString());
      return {
        success: false,
        message: 'فشل في تحديث المخزون: ' + error.toString()
      };
    }
  }

  /**
   * Get low stock products
   */
  getLowStockProducts() {
    try {
      const allProducts = DB.find(this.moduleName);
      const lowStockProducts = allProducts.filter(product => 
        product.isTrackable && (parseFloat(product.currentStock) || 0) <= (parseFloat(product.reorderLevel) || 0)
      );

      return {
        success: true,
        data: lowStockProducts,
        count: lowStockProducts.length
      };
    } catch (error) {
      Logger.log('Error getting low stock products: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب المنتجات منخفضة المخزون: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Generate product code
   */
  generateProductCode() {
    const prefix = 'PRD';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 3).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Get product categories
   */
  getProductCategories() {
    try {
      const allProducts = DB.find(this.moduleName);
      const categories = [...new Set(allProducts.map(p => p.category).filter(c => c))];
      
      return {
        success: true,
        data: categories.sort()
      };
    } catch (error) {
      Logger.log('Error getting product categories: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب فئات المنتجات: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get product brands
   */
  getProductBrands() {
    try {
      const allProducts = DB.find(this.moduleName);
      const brands = [...new Set(allProducts.map(p => p.brand).filter(b => b))];
      
      return {
        success: true,
        data: brands.sort()
      };
    } catch (error) {
      Logger.log('Error getting product brands: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب العلامات التجارية: ' + error.toString(),
        data: []
      };
    }
  }
}

// Global Products instance
const PRODUCTS = new ProductsController();

// Exposed functions for Google Apps Script
function createProduct(productData) {
  return PRODUCTS.createProduct(productData);
}

function getAllProducts(filters) {
  return PRODUCTS.getAllProducts(filters);
}

function getProductById(productId) {
  return PRODUCTS.getProductById(productId);
}

function updateProduct(productId, updateData) {
  return PRODUCTS.updateProduct(productId, updateData);
}

function deleteProduct(productId) {
  return PRODUCTS.deleteProduct(productId);
}

function searchProducts(searchTerm) {
  return PRODUCTS.searchProducts(searchTerm);
}

function getProductStats() {
  return PRODUCTS.getProductStats();
}

function updateProductStock(productId, quantity, operation) {
  return PRODUCTS.updateProductStock(productId, quantity, operation);
}

function getLowStockProducts() {
  return PRODUCTS.getLowStockProducts();
}

function getProductCategories() {
  return PRODUCTS.getProductCategories();
}

function getProductBrands() {
  return PRODUCTS.getProductBrands();
}
