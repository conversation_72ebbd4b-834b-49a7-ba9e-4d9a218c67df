/**
 * CRM Controller - Customer Relationship Management
 * Handles all CRM operations and business logic
 */

class CRMController {
  constructor() {
    this.moduleName = 'CRM';
    this.schema = {
      id: { type: 'string', width: 120 },
      customerCode: { type: 'string', width: 100 },
      name: { type: 'string', width: 200 },
      email: { type: 'string', width: 200 },
      phone: { type: 'string', width: 150 },
      mobile: { type: 'string', width: 150 },
      address: { type: 'string', width: 300 },
      city: { type: 'string', width: 120 },
      country: { type: 'string', width: 120 },
      customerType: { type: 'string', width: 100 },
      creditLimit: { type: 'number', width: 120 },
      currentBalance: { type: 'number', width: 120 },
      salesRepresentative: { type: 'string', width: 150 },
      notes: { type: 'string', width: 300 },
      isActive: { type: 'boolean', width: 80 },
      createdAt: { type: 'date', width: 150 },
      updatedAt: { type: 'date', width: 150 }
    };
    
    this.initializeModule();
  }

  /**
   * Initialize CRM module
   */
  initializeModule() {
    try {
      DB.initializeSchema(this.moduleName, this.schema);
      Logger.log('CRM module initialized successfully');
    } catch (error) {
      Logger.log('Error initializing CRM module: ' + error.toString());
      throw error;
    }
  }

  /**
   * Create a new customer
   */
  createCustomer(customerData) {
    try {
      // Validate required fields
      if (!customerData.name) {
        throw new Error('اسم العميل مطلوب');
      }

      // Generate customer code if not provided
      if (!customerData.customerCode) {
        customerData.customerCode = this.generateCustomerCode();
      }

      // Set default values
      customerData.customerType = customerData.customerType || 'عادي';
      customerData.creditLimit = customerData.creditLimit || 0;
      customerData.currentBalance = customerData.currentBalance || 0;
      customerData.isActive = customerData.isActive !== false;

      // Insert customer
      const customer = DB.insert(this.moduleName, customerData);
      
      Logger.log('Customer created: ' + customer.id);
      return {
        success: true,
        data: customer,
        message: 'تم إنشاء العميل بنجاح'
      };
    } catch (error) {
      Logger.log('Error creating customer: ' + error.toString());
      return {
        success: false,
        message: 'فشل في إنشاء العميل: ' + error.toString()
      };
    }
  }

  /**
   * Get all customers
   */
  getAllCustomers(filters = {}) {
    try {
      let customers = DB.find(this.moduleName, filters);
      
      // Sort by creation date (newest first)
      customers.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      return {
        success: true,
        data: customers,
        count: customers.length
      };
    } catch (error) {
      Logger.log('Error getting customers: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب بيانات العملاء: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get customer by ID
   */
  getCustomerById(customerId) {
    try {
      const customer = DB.findById(this.moduleName, customerId);
      
      if (!customer) {
        return {
          success: false,
          message: 'العميل غير موجود'
        };
      }

      return {
        success: true,
        data: customer
      };
    } catch (error) {
      Logger.log('Error getting customer by ID: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب بيانات العميل: ' + error.toString()
      };
    }
  }

  /**
   * Update customer
   */
  updateCustomer(customerId, updateData) {
    try {
      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.customerCode;

      const updatedCustomer = DB.update(this.moduleName, customerId, updateData);
      
      if (!updatedCustomer) {
        return {
          success: false,
          message: 'العميل غير موجود'
        };
      }

      Logger.log('Customer updated: ' + customerId);
      return {
        success: true,
        data: updatedCustomer,
        message: 'تم تحديث بيانات العميل بنجاح'
      };
    } catch (error) {
      Logger.log('Error updating customer: ' + error.toString());
      return {
        success: false,
        message: 'فشل في تحديث بيانات العميل: ' + error.toString()
      };
    }
  }

  /**
   * Delete customer
   */
  deleteCustomer(customerId) {
    try {
      const deleted = DB.delete(this.moduleName, customerId);
      
      if (!deleted) {
        return {
          success: false,
          message: 'العميل غير موجود'
        };
      }

      Logger.log('Customer deleted: ' + customerId);
      return {
        success: true,
        message: 'تم حذف العميل بنجاح'
      };
    } catch (error) {
      Logger.log('Error deleting customer: ' + error.toString());
      return {
        success: false,
        message: 'فشل في حذف العميل: ' + error.toString()
      };
    }
  }

  /**
   * Search customers
   */
  searchCustomers(searchTerm) {
    try {
      const allCustomers = DB.find(this.moduleName);
      
      if (!searchTerm) {
        return {
          success: true,
          data: allCustomers,
          count: allCustomers.length
        };
      }

      const searchResults = allCustomers.filter(customer => {
        const searchFields = [
          customer.name,
          customer.customerCode,
          customer.email,
          customer.phone,
          customer.mobile,
          customer.city
        ];
        
        return searchFields.some(field => 
          field && field.toString().toLowerCase().includes(searchTerm.toLowerCase())
        );
      });

      return {
        success: true,
        data: searchResults,
        count: searchResults.length
      };
    } catch (error) {
      Logger.log('Error searching customers: ' + error.toString());
      return {
        success: false,
        message: 'فشل في البحث عن العملاء: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get customer statistics
   */
  getCustomerStats() {
    try {
      const allCustomers = DB.find(this.moduleName);
      
      const stats = {
        totalCustomers: allCustomers.length,
        activeCustomers: allCustomers.filter(c => c.isActive).length,
        inactiveCustomers: allCustomers.filter(c => !c.isActive).length,
        totalCreditLimit: allCustomers.reduce((sum, c) => sum + (parseFloat(c.creditLimit) || 0), 0),
        totalBalance: allCustomers.reduce((sum, c) => sum + (parseFloat(c.currentBalance) || 0), 0),
        customersByType: {}
      };

      // Group by customer type
      allCustomers.forEach(customer => {
        const type = customer.customerType || 'غير محدد';
        stats.customersByType[type] = (stats.customersByType[type] || 0) + 1;
      });

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      Logger.log('Error getting customer stats: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب إحصائيات العملاء: ' + error.toString()
      };
    }
  }

  /**
   * Generate customer code
   */
  generateCustomerCode() {
    const prefix = 'CUST';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 3).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Export customers to CSV
   */
  exportCustomers() {
    try {
      const customers = DB.find(this.moduleName);
      
      if (customers.length === 0) {
        return {
          success: false,
          message: 'لا توجد بيانات للتصدير'
        };
      }

      // Create CSV content
      const headers = Object.keys(this.schema);
      const csvContent = [
        headers.join(','),
        ...customers.map(customer => 
          headers.map(header => customer[header] || '').join(',')
        )
      ].join('\n');

      return {
        success: true,
        data: csvContent,
        filename: `customers_export_${new Date().toISOString().split('T')[0]}.csv`
      };
    } catch (error) {
      Logger.log('Error exporting customers: ' + error.toString());
      return {
        success: false,
        message: 'فشل في تصدير البيانات: ' + error.toString()
      };
    }
  }

  /**
   * Get customers with outstanding balance
   */
  getCustomersWithBalance() {
    try {
      const customers = DB.find(this.moduleName);
      const customersWithBalance = customers.filter(customer => 
        parseFloat(customer.currentBalance) > 0
      );

      return {
        success: true,
        data: customersWithBalance,
        count: customersWithBalance.length
      };
    } catch (error) {
      Logger.log('Error getting customers with balance: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب العملاء المدينين: ' + error.toString(),
        data: []
      };
    }
  }
}

// Global CRM instance
const CRM = new CRMController();

// Exposed functions for Google Apps Script
function createCustomer(customerData) {
  return CRM.createCustomer(customerData);
}

function getAllCustomers(filters) {
  return CRM.getAllCustomers(filters);
}

function getCustomerById(customerId) {
  return CRM.getCustomerById(customerId);
}

function updateCustomer(customerId, updateData) {
  return CRM.updateCustomer(customerId, updateData);
}

function deleteCustomer(customerId) {
  return CRM.deleteCustomer(customerId);
}

function searchCustomers(searchTerm) {
  return CRM.searchCustomers(searchTerm);
}

function getCustomerStats() {
  return CRM.getCustomerStats();
}

function exportCustomers() {
  return CRM.exportCustomers();
}

function getCustomersWithBalance() {
  return CRM.getCustomersWithBalance();
}
