/**
 * Sales Controller - Sales and POS Management
 * Handles all sales operations and point of sale functionality
 */

class SalesController {
  constructor() {
    this.moduleName = 'Sales';
    this.schema = {
      id: { type: 'string', width: 120 },
      invoiceNumber: { type: 'string', width: 120 },
      customerId: { type: 'string', width: 120 },
      customerName: { type: 'string', width: 200 },
      saleDate: { type: 'date', width: 120 },
      dueDate: { type: 'date', width: 120 },
      salesRepresentative: { type: 'string', width: 150 },
      subtotal: { type: 'number', width: 100 },
      taxAmount: { type: 'number', width: 100 },
      discountAmount: { type: 'number', width: 100 },
      totalAmount: { type: 'number', width: 120 },
      paidAmount: { type: 'number', width: 100 },
      remainingAmount: { type: 'number', width: 120 },
      paymentMethod: { type: 'string', width: 100 },
      paymentStatus: { type: 'string', width: 100 },
      invoiceStatus: { type: 'string', width: 100 },
      notes: { type: 'string', width: 300 },
      createdAt: { type: 'date', width: 150 },
      updatedAt: { type: 'date', width: 150 }
    };
    
    this.itemsSchema = {
      id: { type: 'string', width: 120 },
      invoiceId: { type: 'string', width: 120 },
      productId: { type: 'string', width: 120 },
      productName: { type: 'string', width: 200 },
      productCode: { type: 'string', width: 100 },
      quantity: { type: 'number', width: 80 },
      unitPrice: { type: 'number', width: 100 },
      discount: { type: 'number', width: 80 },
      taxRate: { type: 'number', width: 80 },
      lineTotal: { type: 'number', width: 120 },
      createdAt: { type: 'date', width: 150 }
    };
    
    this.initializeModule();
  }

  /**
   * Initialize Sales module
   */
  initializeModule() {
    try {
      DB.initializeSchema(this.moduleName, this.schema);
      DB.initializeSchema(this.moduleName + '_Items', this.itemsSchema);
      Logger.log('Sales module initialized successfully');
    } catch (error) {
      Logger.log('Error initializing Sales module: ' + error.toString());
      throw error;
    }
  }

  /**
   * Create a new sales invoice
   */
  createInvoice(invoiceData) {
    try {
      // Validate required fields
      if (!invoiceData.customerId) {
        throw new Error('العميل مطلوب');
      }
      if (!invoiceData.items || invoiceData.items.length === 0) {
        throw new Error('يجب إضافة عنصر واحد على الأقل');
      }

      // Generate invoice number if not provided
      if (!invoiceData.invoiceNumber) {
        invoiceData.invoiceNumber = this.generateInvoiceNumber();
      }

      // Calculate totals
      const calculations = this.calculateInvoiceTotals(invoiceData.items);
      
      // Set invoice data
      const invoice = {
        invoiceNumber: invoiceData.invoiceNumber,
        customerId: invoiceData.customerId,
        customerName: invoiceData.customerName,
        saleDate: invoiceData.saleDate || new Date().toISOString(),
        dueDate: invoiceData.dueDate,
        salesRepresentative: invoiceData.salesRepresentative,
        subtotal: calculations.subtotal,
        taxAmount: calculations.taxAmount,
        discountAmount: calculations.discountAmount,
        totalAmount: calculations.totalAmount,
        paidAmount: invoiceData.paidAmount || 0,
        remainingAmount: calculations.totalAmount - (invoiceData.paidAmount || 0),
        paymentMethod: invoiceData.paymentMethod || 'نقدي',
        paymentStatus: this.determinePaymentStatus(calculations.totalAmount, invoiceData.paidAmount || 0),
        invoiceStatus: invoiceData.invoiceStatus || 'مؤكدة',
        notes: invoiceData.notes || ''
      };

      // Insert invoice
      const savedInvoice = DB.insert(this.moduleName, invoice);

      // Insert invoice items
      const savedItems = [];
      for (const item of invoiceData.items) {
        const itemData = {
          invoiceId: savedInvoice.id,
          productId: item.productId,
          productName: item.productName,
          productCode: item.productCode,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount || 0,
          taxRate: item.taxRate || 0,
          lineTotal: this.calculateLineTotal(item)
        };
        
        const savedItem = DB.insert(this.moduleName + '_Items', itemData);
        savedItems.push(savedItem);
      }

      Logger.log('Invoice created: ' + savedInvoice.id);
      return {
        success: true,
        data: {
          invoice: savedInvoice,
          items: savedItems
        },
        message: 'تم إنشاء الفاتورة بنجاح'
      };
    } catch (error) {
      Logger.log('Error creating invoice: ' + error.toString());
      return {
        success: false,
        message: 'فشل في إنشاء الفاتورة: ' + error.toString()
      };
    }
  }

  /**
   * Get all invoices
   */
  getAllInvoices(filters = {}) {
    try {
      let invoices = DB.find(this.moduleName, filters);
      
      // Sort by creation date (newest first)
      invoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      return {
        success: true,
        data: invoices,
        count: invoices.length
      };
    } catch (error) {
      Logger.log('Error getting invoices: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب الفواتير: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get invoice by ID with items
   */
  getInvoiceById(invoiceId) {
    try {
      const invoice = DB.findById(this.moduleName, invoiceId);
      
      if (!invoice) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة'
        };
      }

      // Get invoice items
      const items = DB.find(this.moduleName + '_Items', { invoiceId: invoiceId });

      return {
        success: true,
        data: {
          invoice: invoice,
          items: items
        }
      };
    } catch (error) {
      Logger.log('Error getting invoice by ID: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب الفاتورة: ' + error.toString()
      };
    }
  }

  /**
   * Update invoice
   */
  updateInvoice(invoiceId, updateData) {
    try {
      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.invoiceNumber;

      // Recalculate remaining amount if paid amount changed
      if (updateData.paidAmount !== undefined) {
        const invoice = DB.findById(this.moduleName, invoiceId);
        if (invoice) {
          updateData.remainingAmount = invoice.totalAmount - updateData.paidAmount;
          updateData.paymentStatus = this.determinePaymentStatus(invoice.totalAmount, updateData.paidAmount);
        }
      }

      const updatedInvoice = DB.update(this.moduleName, invoiceId, updateData);
      
      if (!updatedInvoice) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة'
        };
      }

      Logger.log('Invoice updated: ' + invoiceId);
      return {
        success: true,
        data: updatedInvoice,
        message: 'تم تحديث الفاتورة بنجاح'
      };
    } catch (error) {
      Logger.log('Error updating invoice: ' + error.toString());
      return {
        success: false,
        message: 'فشل في تحديث الفاتورة: ' + error.toString()
      };
    }
  }

  /**
   * Delete invoice
   */
  deleteInvoice(invoiceId) {
    try {
      // Delete invoice items first
      const items = DB.find(this.moduleName + '_Items', { invoiceId: invoiceId });
      for (const item of items) {
        DB.delete(this.moduleName + '_Items', item.id);
      }

      // Delete invoice
      const deleted = DB.delete(this.moduleName, invoiceId);
      
      if (!deleted) {
        return {
          success: false,
          message: 'الفاتورة غير موجودة'
        };
      }

      Logger.log('Invoice deleted: ' + invoiceId);
      return {
        success: true,
        message: 'تم حذف الفاتورة بنجاح'
      };
    } catch (error) {
      Logger.log('Error deleting invoice: ' + error.toString());
      return {
        success: false,
        message: 'فشل في حذف الفاتورة: ' + error.toString()
      };
    }
  }

  /**
   * Search invoices
   */
  searchInvoices(searchTerm) {
    try {
      const allInvoices = DB.find(this.moduleName);
      
      if (!searchTerm) {
        return {
          success: true,
          data: allInvoices,
          count: allInvoices.length
        };
      }

      const searchResults = allInvoices.filter(invoice => {
        const searchFields = [
          invoice.invoiceNumber,
          invoice.customerName,
          invoice.salesRepresentative,
          invoice.paymentMethod,
          invoice.invoiceStatus
        ];
        
        return searchFields.some(field => 
          field && field.toString().toLowerCase().includes(searchTerm.toLowerCase())
        );
      });

      return {
        success: true,
        data: searchResults,
        count: searchResults.length
      };
    } catch (error) {
      Logger.log('Error searching invoices: ' + error.toString());
      return {
        success: false,
        message: 'فشل في البحث عن الفواتير: ' + error.toString(),
        data: []
      };
    }
  }

  /**
   * Get sales statistics
   */
  getSalesStats() {
    try {
      const allInvoices = DB.find(this.moduleName);
      const today = new Date().toISOString().split('T')[0];
      const thisMonth = new Date().toISOString().substring(0, 7);
      
      const stats = {
        totalInvoices: allInvoices.length,
        todayInvoices: allInvoices.filter(inv => inv.saleDate && inv.saleDate.startsWith(today)).length,
        thisMonthInvoices: allInvoices.filter(inv => inv.saleDate && inv.saleDate.startsWith(thisMonth)).length,
        totalSales: allInvoices.reduce((sum, inv) => sum + (parseFloat(inv.totalAmount) || 0), 0),
        todaySales: allInvoices
          .filter(inv => inv.saleDate && inv.saleDate.startsWith(today))
          .reduce((sum, inv) => sum + (parseFloat(inv.totalAmount) || 0), 0),
        thisMonthSales: allInvoices
          .filter(inv => inv.saleDate && inv.saleDate.startsWith(thisMonth))
          .reduce((sum, inv) => sum + (parseFloat(inv.totalAmount) || 0), 0),
        paidAmount: allInvoices.reduce((sum, inv) => sum + (parseFloat(inv.paidAmount) || 0), 0),
        remainingAmount: allInvoices.reduce((sum, inv) => sum + (parseFloat(inv.remainingAmount) || 0), 0),
        invoicesByStatus: {},
        invoicesByPaymentStatus: {}
      };

      // Group by status
      allInvoices.forEach(invoice => {
        const status = invoice.invoiceStatus || 'غير محدد';
        stats.invoicesByStatus[status] = (stats.invoicesByStatus[status] || 0) + 1;
        
        const paymentStatus = invoice.paymentStatus || 'غير محدد';
        stats.invoicesByPaymentStatus[paymentStatus] = (stats.invoicesByPaymentStatus[paymentStatus] || 0) + 1;
      });

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      Logger.log('Error getting sales stats: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب إحصائيات المبيعات: ' + error.toString()
      };
    }
  }

  /**
   * Calculate invoice totals
   */
  calculateInvoiceTotals(items) {
    let subtotal = 0;
    let taxAmount = 0;
    let discountAmount = 0;

    items.forEach(item => {
      const lineTotal = this.calculateLineTotal(item);
      subtotal += lineTotal;
      
      if (item.discount) {
        discountAmount += (item.quantity * item.unitPrice * item.discount / 100);
      }
      
      if (item.taxRate) {
        taxAmount += (lineTotal * item.taxRate / 100);
      }
    });

    const totalAmount = subtotal + taxAmount - discountAmount;

    return {
      subtotal: subtotal,
      taxAmount: taxAmount,
      discountAmount: discountAmount,
      totalAmount: totalAmount
    };
  }

  /**
   * Calculate line total for an item
   */
  calculateLineTotal(item) {
    const baseAmount = item.quantity * item.unitPrice;
    const discountAmount = baseAmount * (item.discount || 0) / 100;
    const afterDiscount = baseAmount - discountAmount;
    const taxAmount = afterDiscount * (item.taxRate || 0) / 100;
    return afterDiscount + taxAmount;
  }

  /**
   * Determine payment status
   */
  determinePaymentStatus(totalAmount, paidAmount) {
    if (paidAmount >= totalAmount) {
      return 'مدفوعة';
    } else if (paidAmount > 0) {
      return 'مدفوعة جزئياً';
    } else {
      return 'غير مدفوعة';
    }
  }

  /**
   * Generate invoice number
   */
  generateInvoiceNumber() {
    const prefix = 'INV';
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const time = Date.now().toString().slice(-4);
    return `${prefix}${year}${month}${day}${time}`;
  }

  /**
   * Get top selling products
   */
  getTopSellingProducts(limit = 10) {
    try {
      const allItems = DB.find(this.moduleName + '_Items');
      const productSales = {};

      allItems.forEach(item => {
        const productId = item.productId;
        if (!productSales[productId]) {
          productSales[productId] = {
            productId: productId,
            productName: item.productName,
            productCode: item.productCode,
            totalQuantity: 0,
            totalSales: 0
          };
        }
        
        productSales[productId].totalQuantity += parseFloat(item.quantity) || 0;
        productSales[productId].totalSales += parseFloat(item.lineTotal) || 0;
      });

      const sortedProducts = Object.values(productSales)
        .sort((a, b) => b.totalSales - a.totalSales)
        .slice(0, limit);

      return {
        success: true,
        data: sortedProducts
      };
    } catch (error) {
      Logger.log('Error getting top selling products: ' + error.toString());
      return {
        success: false,
        message: 'فشل في جلب المنتجات الأكثر مبيعاً: ' + error.toString(),
        data: []
      };
    }
  }
}

// Global Sales instance
const SALES = new SalesController();

// Exposed functions for Google Apps Script
function createInvoice(invoiceData) {
  return SALES.createInvoice(invoiceData);
}

function getAllInvoices(filters) {
  return SALES.getAllInvoices(filters);
}

function getInvoiceById(invoiceId) {
  return SALES.getInvoiceById(invoiceId);
}

function updateInvoice(invoiceId, updateData) {
  return SALES.updateInvoice(invoiceId, updateData);
}

function deleteInvoice(invoiceId) {
  return SALES.deleteInvoice(invoiceId);
}

function searchInvoices(searchTerm) {
  return SALES.searchInvoices(searchTerm);
}

function getSalesStats() {
  return SALES.getSalesStats();
}

function getTopSellingProducts(limit) {
  return SALES.getTopSellingProducts(limit);
}
