<script>
/**
 * ERP System - Main JavaScript Controller
 * Handles SPA navigation, module loading, and global functions
 */

// Global ERP Namespace
window.ERP = {
    currentModule: null,
    currentUser: null,
    isLoading: false,
    
    // Initialize the application
    init: function() {
        console.log('Initializing ERP System...');
        this.showLoading();
        this.setupEventListeners();
        this.testConnection();
    },
    
    // Show loading screen with progress
    showLoading: function(message = 'جاري تحميل النظام...') {
        const loadingScreen = document.getElementById('loadingScreen');
        const appContainer = document.getElementById('appContainer');
        
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
            loadingScreen.querySelector('p').textContent = message;
        }
        
        if (appContainer) {
            appContainer.classList.add('hidden');
        }
        
        // Simulate loading progress
        this.updateLoadingProgress();
    },
    
    // Hide loading screen
    hideLoading: function() {
        const loadingScreen = document.getElementById('loadingScreen');
        const appContainer = document.getElementById('appContainer');
        
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
            }
            if (appContainer) {
                appContainer.classList.remove('hidden');
            }
        }, 500);
    },
    
    // Update loading progress bar
    updateLoadingProgress: function() {
        const progressBar = document.getElementById('loadingProgress');
        let progress = 0;
        
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
        }, 200);
    },
    
    // Setup event listeners
    setupEventListeners: function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }
        
        // Mobile sidebar
        this.setupMobileSidebar();
    },
    
    // Setup mobile sidebar
    setupMobileSidebar: function() {
        if (window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (sidebar && sidebarToggle) {
                sidebarToggle.addEventListener('click', () => {
                    sidebar.classList.toggle('show');
                });
                
                // Close sidebar when clicking outside
                document.addEventListener('click', (e) => {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        }
    },
    
    // Test system connection
    testConnection: function() {
        google.script.run
            .withSuccessHandler((result) => {
                if (result.success) {
                    this.currentUser = result.user;
                    this.updateUserInfo();
                    this.loadDashboard();
                } else {
                    this.showError('فشل في الاتصال بالنظام: ' + result.message);
                }
            })
            .withFailureHandler((error) => {
                this.showError('خطأ في الاتصال: ' + error.toString());
            })
            .testConnection();
    },
    
    // Update user information in UI
    updateUserInfo: function() {
        const userName = document.getElementById('userName');
        if (userName && this.currentUser) {
            userName.textContent = this.currentUser.name || this.currentUser.email;
        }
    },
    
    // Load dashboard
    loadDashboard: function() {
        this.showLoading('جاري تحميل لوحة التحكم...');
        
        google.script.run
            .withSuccessHandler((result) => {
                if (result.success) {
                    this.renderDashboard(result);
                    this.hideLoading();
                } else {
                    this.showError('فشل في تحميل لوحة التحكم: ' + result.message);
                }
            })
            .withFailureHandler((error) => {
                this.showError('خطأ في تحميل لوحة التحكم: ' + error.toString());
            })
            .getDashboardData();
    },
    
    // Render dashboard
    renderDashboard: function(data) {
        const contentArea = document.getElementById('contentArea');
        if (!contentArea) return;
        
        const dashboardHTML = `
            <div class="animate-fade-in">
                <div class="mb-6">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">مرحباً، ${data.user.name}</h2>
                    <p class="text-gray-600">لوحة التحكم الرئيسية - ${data.systemInfo.name}</p>
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stats-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80">إجمالي العملاء</p>
                                <p class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-users text-4xl text-white text-opacity-60"></i>
                        </div>
                    </div>
                    
                    <div class="stats-card success">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80">المبيعات اليوم</p>
                                <p class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-shopping-cart text-4xl text-white text-opacity-60"></i>
                        </div>
                    </div>
                    
                    <div class="stats-card warning">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80">المنتجات</p>
                                <p class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-box text-4xl text-white text-opacity-60"></i>
                        </div>
                    </div>
                    
                    <div class="stats-card danger">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80">المخزون المنخفض</p>
                                <p class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-exclamation-triangle text-4xl text-white text-opacity-60"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title mb-4">
                                <i class="fas fa-bolt text-primary ml-2"></i>
                                إجراءات سريعة
                            </h5>
                            <div class="grid grid-cols-2 gap-3">
                                <button onclick="openModule('Sales')" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus ml-1"></i> فاتورة جديدة
                                </button>
                                <button onclick="openModule('CRM')" class="btn btn-success btn-sm">
                                    <i class="fas fa-user-plus ml-1"></i> عميل جديد
                                </button>
                                <button onclick="openModule('Products')" class="btn btn-info btn-sm">
                                    <i class="fas fa-box ml-1"></i> منتج جديد
                                </button>
                                <button onclick="openModule('Inventory')" class="btn btn-warning btn-sm">
                                    <i class="fas fa-warehouse ml-1"></i> جرد المخزون
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title mb-4">
                                <i class="fas fa-chart-line text-primary ml-2"></i>
                                الإحصائيات السريعة
                            </h5>
                            <div class="chart-container" style="height: 200px;">
                                <canvas id="dashboardChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activities -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-4">
                            <i class="fas fa-clock text-primary ml-2"></i>
                            الأنشطة الأخيرة
                        </h5>
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-inbox text-6xl mb-4"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        contentArea.innerHTML = dashboardHTML;
        this.currentModule = 'Dashboard';
        this.updateActiveModule();
        this.initDashboardChart();
    },
    
    // Initialize dashboard chart
    initDashboardChart: function() {
        const ctx = document.getElementById('dashboardChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [0, 0, 0, 0, 0, 0],
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    },
    
    // Open module
    openModule: function(moduleName) {
        if (this.isLoading) return;
        
        this.showLoading(`جاري تحميل موديول ${moduleName}...`);
        this.isLoading = true;
        
        google.script.run
            .withSuccessHandler((result) => {
                this.isLoading = false;
                if (result.success) {
                    this.renderModule(moduleName, result.content);
                    this.hideLoading();
                } else {
                    this.showError('فشل في تحميل الموديول: ' + result.message);
                }
            })
            .withFailureHandler((error) => {
                this.isLoading = false;
                this.showError('خطأ في تحميل الموديول: ' + error.toString());
            })
            .loadModule(moduleName);
    },
    
    // Render module content
    renderModule: function(moduleName, content) {
        const contentArea = document.getElementById('contentArea');
        if (contentArea) {
            contentArea.innerHTML = content || `
                <div class="text-center py-12">
                    <i class="fas fa-cog text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-600 mb-2">موديول ${moduleName}</h3>
                    <p class="text-gray-500">هذا الموديول قيد التطوير</p>
                    <button onclick="goToDashboard()" class="btn btn-primary mt-4">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </button>
                </div>
            `;
        }
        
        this.currentModule = moduleName;
        this.updateActiveModule();
        this.executePageScripts();
    },
    
    // Update active module in sidebar
    updateActiveModule: function() {
        // Remove active class from all module buttons
        document.querySelectorAll('.module-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Add active class to current module
        if (this.currentModule && this.currentModule !== 'Dashboard') {
            const moduleBtn = document.querySelector(`[onclick="openModule('${this.currentModule}')"]`);
            if (moduleBtn) {
                moduleBtn.classList.add('active');
            }
        }
    },
    
    // Execute page-specific scripts
    executePageScripts: function() {
        // This function can be overridden by modules
        if (window.moduleInit && typeof window.moduleInit === 'function') {
            window.moduleInit();
        }
    },
    
    // Go to dashboard
    goToDashboard: function() {
        this.loadDashboard();
    },
    
    // Refresh data
    refreshData: function() {
        if (this.currentModule === 'Dashboard' || !this.currentModule) {
            this.loadDashboard();
        } else {
            this.openModule(this.currentModule);
        }
    },
    
    // Show error message
    showError: function(message) {
        this.hideLoading();
        this.showAlert(message, 'danger');
    },
    
    // Show success message
    showSuccess: function(message) {
        this.showAlert(message, 'success');
    },
    
    // Show alert
    showAlert: function(message, type = 'info') {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} ml-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const contentArea = document.getElementById('contentArea');
        if (contentArea) {
            contentArea.insertAdjacentHTML('afterbegin', alertHTML);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = contentArea.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }
};

// Global Functions (for backward compatibility)
window.openModule = function(moduleName) {
    window.ERP.openModule(moduleName);
};

window.goToDashboard = function() {
    window.ERP.goToDashboard();
};

window.goBack = function() {
    window.ERP.goToDashboard();
};

window.refreshData = function() {
    window.ERP.refreshData();
};

window.showProfile = function() {
    window.ERP.showAlert('الملف الشخصي قيد التطوير', 'info');
};

window.showSettings = function() {
    window.ERP.openModule('Settings');
};

window.logout = function() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.location.reload();
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.ERP.init();
});

// Handle window resize for responsive sidebar
window.addEventListener('resize', function() {
    window.ERP.setupMobileSidebar();
});
</script>
