/**
 * Database Manager - Google Sheets as Database
 * Handles all database operations using Google Sheets
 * Provides CRUD operations and schema management
 */

class DatabaseManager {
  constructor() {
    this.spreadsheetPrefix = ERP_CONFIG.SPREADSHEET_PREFIX;
    this.cache = new Map();
  }

  /**
   * Get or create a spreadsheet for a module
   */
  getSpreadsheet(moduleName) {
    try {
      const spreadsheetName = this.spreadsheetPrefix + moduleName;
      
      // Check cache first
      if (this.cache.has(spreadsheetName)) {
        return this.cache.get(spreadsheetName);
      }
      
      // Try to find existing spreadsheet
      const files = DriveApp.getFilesByName(spreadsheetName);
      let spreadsheet;
      
      if (files.hasNext()) {
        const file = files.next();
        spreadsheet = SpreadsheetApp.openById(file.getId());
      } else {
        // Create new spreadsheet
        spreadsheet = SpreadsheetApp.create(spreadsheetName);
        Logger.log(`Created new spreadsheet: ${spreadsheetName}`);
      }
      
      // Cache the spreadsheet
      this.cache.set(spreadsheetName, spreadsheet);
      return spreadsheet;
    } catch (error) {
      Logger.log(`Error getting spreadsheet for ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Get or create a sheet within a spreadsheet
   */
  getSheet(moduleName, sheetName = 'Data') {
    try {
      const spreadsheet = this.getSpreadsheet(moduleName);
      let sheet = spreadsheet.getSheetByName(sheetName);
      
      if (!sheet) {
        sheet = spreadsheet.insertSheet(sheetName);
        Logger.log(`Created new sheet: ${sheetName} in ${moduleName}`);
      }
      
      return sheet;
    } catch (error) {
      Logger.log(`Error getting sheet ${sheetName} for ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Initialize schema for a module
   */
  initializeSchema(moduleName, schema) {
    try {
      const sheet = this.getSheet(moduleName);
      
      // Check if sheet is already initialized
      if (sheet.getLastRow() > 0) {
        return sheet;
      }
      
      // Set headers
      const headers = Object.keys(schema);
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format headers
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#2563eb');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      headerRange.setHorizontalAlignment('center');
      
      // Set column widths based on schema
      headers.forEach((header, index) => {
        const columnWidth = schema[header].width || 150;
        sheet.setColumnWidth(index + 1, columnWidth);
      });
      
      Logger.log(`Initialized schema for ${moduleName}`);
      return sheet;
    } catch (error) {
      Logger.log(`Error initializing schema for ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Insert a new record
   */
  insert(moduleName, data, sheetName = 'Data') {
    try {
      const sheet = this.getSheet(moduleName, sheetName);
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      
      // Prepare row data
      const rowData = headers.map(header => {
        if (header === 'id' && !data.id) {
          return this.generateId();
        }
        if (header === 'createdAt' && !data.createdAt) {
          return new Date().toISOString();
        }
        if (header === 'updatedAt') {
          return new Date().toISOString();
        }
        return data[header] || '';
      });
      
      // Insert the row
      const newRow = sheet.getLastRow() + 1;
      sheet.getRange(newRow, 1, 1, rowData.length).setValues([rowData]);
      
      // Return the inserted record with ID
      const result = {};
      headers.forEach((header, index) => {
        result[header] = rowData[index];
      });
      
      Logger.log(`Inserted record in ${moduleName}: ${result.id}`);
      return result;
    } catch (error) {
      Logger.log(`Error inserting record in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Find records by criteria
   */
  find(moduleName, criteria = {}, sheetName = 'Data') {
    try {
      const sheet = this.getSheet(moduleName, sheetName);
      const lastRow = sheet.getLastRow();
      
      if (lastRow <= 1) {
        return [];
      }
      
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      const data = sheet.getRange(2, 1, lastRow - 1, headers.length).getValues();
      
      // Convert to objects
      const records = data.map(row => {
        const record = {};
        headers.forEach((header, index) => {
          record[header] = row[index];
        });
        return record;
      });
      
      // Apply criteria filter
      if (Object.keys(criteria).length === 0) {
        return records;
      }
      
      return records.filter(record => {
        return Object.keys(criteria).every(key => {
          if (criteria[key] === null || criteria[key] === undefined) {
            return true;
          }
          return record[key] === criteria[key];
        });
      });
    } catch (error) {
      Logger.log(`Error finding records in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Find a single record by ID
   */
  findById(moduleName, id, sheetName = 'Data') {
    try {
      const records = this.find(moduleName, { id: id }, sheetName);
      return records.length > 0 ? records[0] : null;
    } catch (error) {
      Logger.log(`Error finding record by ID in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Update a record by ID
   */
  update(moduleName, id, updateData, sheetName = 'Data') {
    try {
      const sheet = this.getSheet(moduleName, sheetName);
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      const lastRow = sheet.getLastRow();
      
      if (lastRow <= 1) {
        throw new Error('No records found');
      }
      
      // Find the row with the matching ID
      const idColumnIndex = headers.indexOf('id');
      if (idColumnIndex === -1) {
        throw new Error('ID column not found');
      }
      
      const idColumn = sheet.getRange(2, idColumnIndex + 1, lastRow - 1, 1).getValues();
      const rowIndex = idColumn.findIndex(row => row[0] === id);
      
      if (rowIndex === -1) {
        throw new Error('Record not found');
      }
      
      const actualRowIndex = rowIndex + 2; // +2 because of header and 0-based index
      
      // Update the record
      updateData.updatedAt = new Date().toISOString();
      
      headers.forEach((header, columnIndex) => {
        if (updateData.hasOwnProperty(header) && header !== 'id' && header !== 'createdAt') {
          sheet.getRange(actualRowIndex, columnIndex + 1).setValue(updateData[header]);
        }
      });
      
      Logger.log(`Updated record in ${moduleName}: ${id}`);
      return this.findById(moduleName, id, sheetName);
    } catch (error) {
      Logger.log(`Error updating record in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Delete a record by ID
   */
  delete(moduleName, id, sheetName = 'Data') {
    try {
      const sheet = this.getSheet(moduleName, sheetName);
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      const lastRow = sheet.getLastRow();
      
      if (lastRow <= 1) {
        throw new Error('No records found');
      }
      
      // Find the row with the matching ID
      const idColumnIndex = headers.indexOf('id');
      if (idColumnIndex === -1) {
        throw new Error('ID column not found');
      }
      
      const idColumn = sheet.getRange(2, idColumnIndex + 1, lastRow - 1, 1).getValues();
      const rowIndex = idColumn.findIndex(row => row[0] === id);
      
      if (rowIndex === -1) {
        throw new Error('Record not found');
      }
      
      const actualRowIndex = rowIndex + 2; // +2 because of header and 0-based index
      
      // Delete the row
      sheet.deleteRow(actualRowIndex);
      
      Logger.log(`Deleted record in ${moduleName}: ${id}`);
      return true;
    } catch (error) {
      Logger.log(`Error deleting record in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Count records
   */
  count(moduleName, criteria = {}, sheetName = 'Data') {
    try {
      const records = this.find(moduleName, criteria, sheetName);
      return records.length;
    } catch (error) {
      Logger.log(`Error counting records in ${moduleName}: ${error.toString()}`);
      return 0;
    }
  }

  /**
   * Generate unique ID
   */
  generateId() {
    return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Execute raw query (for complex operations)
   */
  query(moduleName, queryFunction, sheetName = 'Data') {
    try {
      const sheet = this.getSheet(moduleName, sheetName);
      return queryFunction(sheet);
    } catch (error) {
      Logger.log(`Error executing query in ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Backup data
   */
  backup(moduleName) {
    try {
      const spreadsheet = this.getSpreadsheet(moduleName);
      const backupName = `${this.spreadsheetPrefix}${moduleName}_Backup_${new Date().toISOString().split('T')[0]}`;
      
      const backupFile = DriveApp.getFileById(spreadsheet.getId()).makeCopy(backupName);
      
      Logger.log(`Created backup for ${moduleName}: ${backupName}`);
      return backupFile.getId();
    } catch (error) {
      Logger.log(`Error creating backup for ${moduleName}: ${error.toString()}`);
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    Logger.log('Database cache cleared');
  }
}

// Global database instance
const DB = new DatabaseManager();
