<div class="animate-fade-in">
    <!-- <PERSON><PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-users text-primary ml-2"></i>
                إدارة العملاء (CRM)
            </h2>
            <p class="text-gray-600">إدارة شاملة لبيانات العملاء والعلاقات التجارية</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="goToDashboard()">
                <i class="fas fa-arrow-right ml-1"></i> العودة
            </button>
            <button class="btn btn-success" onclick="showAddCustomerModal()">
                <i class="fas fa-plus ml-1"></i> عميل جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-4xl mb-3"></i>
                    <h3 id="totalCustomers" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">إجمالي العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card success">
                <div class="card-body text-center">
                    <i class="fas fa-user-check text-4xl mb-3"></i>
                    <h3 id="activeCustomers" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">العملاء النشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card warning">
                <div class="card-body text-center">
                    <i class="fas fa-coins text-4xl mb-3"></i>
                    <h3 id="totalBalance" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">إجمالي الأرصدة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card danger">
                <div class="card-body text-center">
                    <i class="fas fa-credit-card text-4xl mb-3"></i>
                    <h3 id="totalCreditLimit" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">حد الائتمان</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="البحث في العملاء...">
                        <button class="btn btn-primary" onclick="searchCustomers()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select id="customerTypeFilter" class="form-select" onchange="filterCustomers()">
                        <option value="">جميع الأنواع</option>
                        <option value="عادي">عادي</option>
                        <option value="مميز">مميز</option>
                        <option value="جملة">جملة</option>
                        <option value="تجزئة">تجزئة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select id="statusFilter" class="form-select" onchange="filterCustomers()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list ml-2"></i>
                قائمة العملاء
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success btn-sm" onclick="exportCustomers()">
                    <i class="fas fa-download ml-1"></i> تصدير
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshCustomers()">
                    <i class="fas fa-sync-alt ml-1"></i> تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="customersTableContainer">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات العملاء...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Customer Modal -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalTitle">
                    <i class="fas fa-user-plus ml-2"></i>
                    إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <input type="hidden" id="customerId">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerName" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customerCode" class="form-label">كود العميل</label>
                            <input type="text" class="form-control" id="customerCode" placeholder="سيتم إنشاؤه تلقائياً">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customerPhone" class="form-label">الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerMobile" class="form-label">الجوال</label>
                            <input type="tel" class="form-control" id="customerMobile">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customerType" class="form-label">نوع العميل</label>
                            <select class="form-select" id="customerType">
                                <option value="عادي">عادي</option>
                                <option value="مميز">مميز</option>
                                <option value="جملة">جملة</option>
                                <option value="تجزئة">تجزئة</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerCity" class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="customerCity">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customerCountry" class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="customerCountry" value="السعودية">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">العنوان</label>
                        <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="creditLimit" class="form-label">حد الائتمان</label>
                            <input type="number" class="form-control" id="creditLimit" min="0" step="0.01" value="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="salesRepresentative" class="form-label">المندوب</label>
                            <input type="text" class="form-control" id="salesRepresentative">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="customerNotes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="customerNotes" rows="3"></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive" checked>
                        <label class="form-check-label" for="isActive">
                            عميل نشط
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                    <i class="fas fa-save ml-1"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Details Modal -->
<div class="modal fade" id="customerDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user ml-2"></i>
                    تفاصيل العميل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetailsContent">
                <!-- Customer details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// CRM Module JavaScript
window.moduleInit = function() {
    loadCustomerStats();
    loadCustomers();
    
    // Setup search on Enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchCustomers();
        }
    });
};

// Load customer statistics
function loadCustomerStats() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                const stats = result.data;
                document.getElementById('totalCustomers').textContent = stats.totalCustomers;
                document.getElementById('activeCustomers').textContent = stats.activeCustomers;
                document.getElementById('totalBalance').textContent = formatCurrency(stats.totalBalance);
                document.getElementById('totalCreditLimit').textContent = formatCurrency(stats.totalCreditLimit);
            }
        })
        .withFailureHandler(function(error) {
            console.error('Error loading customer stats:', error);
        })
        .getCustomerStats();
}

// Load customers
function loadCustomers() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                renderCustomersTable(result.data);
            } else {
                showError('فشل في تحميل بيانات العملاء: ' + result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في تحميل بيانات العملاء: ' + error.toString());
        })
        .getAllCustomers();
}

// Render customers table
function renderCustomersTable(customers) {
    const container = document.getElementById('customersTableContainer');
    
    if (customers.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-users text-6xl text-gray-400 mb-4"></i>
                <h4 class="text-gray-600">لا توجد عملاء</h4>
                <p class="text-gray-500">ابدأ بإضافة عميل جديد</p>
                <button class="btn btn-primary" onclick="showAddCustomerModal()">
                    <i class="fas fa-plus ml-1"></i> إضافة عميل
                </button>
            </div>
        `;
        return;
    }
    
    const tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>كود العميل</th>
                        <th>الاسم</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>النوع</th>
                        <th>الرصيد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${customers.map(customer => `
                        <tr>
                            <td><strong>${customer.customerCode || ''}</strong></td>
                            <td>${customer.name || ''}</td>
                            <td>${customer.mobile || customer.phone || ''}</td>
                            <td>${customer.email || ''}</td>
                            <td><span class="badge bg-info">${customer.customerType || 'عادي'}</span></td>
                            <td>${formatCurrency(customer.currentBalance || 0)}</td>
                            <td>
                                <span class="badge ${customer.isActive ? 'bg-success' : 'bg-secondary'}">
                                    ${customer.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewCustomer('${customer.id}')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="editCustomer('${customer.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteCustomerConfirm('${customer.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHTML;
}

// Show add customer modal
function showAddCustomerModal() {
    document.getElementById('customerModalTitle').innerHTML = '<i class="fas fa-user-plus ml-2"></i> إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customerId').value = '';
    document.getElementById('isActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('customerModal'));
    modal.show();
}

// Save customer
function saveCustomer() {
    const form = document.getElementById('customerForm');
    const formData = new FormData(form);
    
    const customerData = {
        name: document.getElementById('customerName').value,
        customerCode: document.getElementById('customerCode').value,
        email: document.getElementById('customerEmail').value,
        phone: document.getElementById('customerPhone').value,
        mobile: document.getElementById('customerMobile').value,
        address: document.getElementById('customerAddress').value,
        city: document.getElementById('customerCity').value,
        country: document.getElementById('customerCountry').value,
        customerType: document.getElementById('customerType').value,
        creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
        salesRepresentative: document.getElementById('salesRepresentative').value,
        notes: document.getElementById('customerNotes').value,
        isActive: document.getElementById('isActive').checked
    };
    
    const customerId = document.getElementById('customerId').value;
    
    if (customerId) {
        // Update existing customer
        google.script.run
            .withSuccessHandler(function(result) {
                if (result.success) {
                    showSuccess(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('customerModal')).hide();
                    loadCustomers();
                    loadCustomerStats();
                } else {
                    showError(result.message);
                }
            })
            .withFailureHandler(function(error) {
                showError('خطأ في تحديث العميل: ' + error.toString());
            })
            .updateCustomer(customerId, customerData);
    } else {
        // Create new customer
        google.script.run
            .withSuccessHandler(function(result) {
                if (result.success) {
                    showSuccess(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('customerModal')).hide();
                    loadCustomers();
                    loadCustomerStats();
                } else {
                    showError(result.message);
                }
            })
            .withFailureHandler(function(error) {
                showError('خطأ في إنشاء العميل: ' + error.toString());
            })
            .createCustomer(customerData);
    }
}

// Search customers
function searchCustomers() {
    const searchTerm = document.getElementById('searchInput').value;
    
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                renderCustomersTable(result.data);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في البحث: ' + error.toString());
        })
        .searchCustomers(searchTerm);
}

// Filter customers
function filterCustomers() {
    const typeFilter = document.getElementById('customerTypeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    const filters = {};
    if (typeFilter) filters.customerType = typeFilter;
    if (statusFilter) filters.isActive = statusFilter === 'active';
    
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                renderCustomersTable(result.data);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في التصفية: ' + error.toString());
        })
        .getAllCustomers(filters);
}

// Refresh customers
function refreshCustomers() {
    loadCustomers();
    loadCustomerStats();
}

// View customer details
function viewCustomer(customerId) {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                showCustomerDetails(result.data);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في جلب تفاصيل العميل: ' + error.toString());
        })
        .getCustomerById(customerId);
}

// Show customer details
function showCustomerDetails(customer) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">المعلومات الأساسية</h6>
                <table class="table table-sm">
                    <tr><td><strong>كود العميل:</strong></td><td>${customer.customerCode || ''}</td></tr>
                    <tr><td><strong>الاسم:</strong></td><td>${customer.name || ''}</td></tr>
                    <tr><td><strong>النوع:</strong></td><td>${customer.customerType || ''}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td><span class="badge ${customer.isActive ? 'bg-success' : 'bg-secondary'}">${customer.isActive ? 'نشط' : 'غير نشط'}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">معلومات الاتصال</h6>
                <table class="table table-sm">
                    <tr><td><strong>البريد الإلكتروني:</strong></td><td>${customer.email || ''}</td></tr>
                    <tr><td><strong>الهاتف:</strong></td><td>${customer.phone || ''}</td></tr>
                    <tr><td><strong>الجوال:</strong></td><td>${customer.mobile || ''}</td></tr>
                    <tr><td><strong>العنوان:</strong></td><td>${customer.address || ''}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h6 class="text-primary">المعلومات المالية</h6>
                <table class="table table-sm">
                    <tr><td><strong>حد الائتمان:</strong></td><td>${formatCurrency(customer.creditLimit || 0)}</td></tr>
                    <tr><td><strong>الرصيد الحالي:</strong></td><td>${formatCurrency(customer.currentBalance || 0)}</td></tr>
                    <tr><td><strong>المندوب:</strong></td><td>${customer.salesRepresentative || ''}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">معلومات إضافية</h6>
                <table class="table table-sm">
                    <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${formatDate(customer.createdAt)}</td></tr>
                    <tr><td><strong>آخر تحديث:</strong></td><td>${formatDate(customer.updatedAt)}</td></tr>
                </table>
                ${customer.notes ? `<p><strong>ملاحظات:</strong><br>${customer.notes}</p>` : ''}
            </div>
        </div>
    `;
    
    document.getElementById('customerDetailsContent').innerHTML = content;
    const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
    modal.show();
}

// Edit customer
function editCustomer(customerId) {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                const customer = result.data;
                
                document.getElementById('customerModalTitle').innerHTML = '<i class="fas fa-user-edit ml-2"></i> تعديل العميل';
                document.getElementById('customerId').value = customer.id;
                document.getElementById('customerName').value = customer.name || '';
                document.getElementById('customerCode').value = customer.customerCode || '';
                document.getElementById('customerEmail').value = customer.email || '';
                document.getElementById('customerPhone').value = customer.phone || '';
                document.getElementById('customerMobile').value = customer.mobile || '';
                document.getElementById('customerAddress').value = customer.address || '';
                document.getElementById('customerCity').value = customer.city || '';
                document.getElementById('customerCountry').value = customer.country || '';
                document.getElementById('customerType').value = customer.customerType || 'عادي';
                document.getElementById('creditLimit').value = customer.creditLimit || 0;
                document.getElementById('salesRepresentative').value = customer.salesRepresentative || '';
                document.getElementById('customerNotes').value = customer.notes || '';
                document.getElementById('isActive').checked = customer.isActive !== false;
                
                const modal = new bootstrap.Modal(document.getElementById('customerModal'));
                modal.show();
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في جلب بيانات العميل: ' + error.toString());
        })
        .getCustomerById(customerId);
}

// Delete customer confirmation
function deleteCustomerConfirm(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.')) {
        google.script.run
            .withSuccessHandler(function(result) {
                if (result.success) {
                    showSuccess(result.message);
                    loadCustomers();
                    loadCustomerStats();
                } else {
                    showError(result.message);
                }
            })
            .withFailureHandler(function(error) {
                showError('خطأ في حذف العميل: ' + error.toString());
            })
            .deleteCustomer(customerId);
    }
}

// Export customers
function exportCustomers() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                downloadCSV(result.data, result.filename);
                showSuccess('تم تصدير البيانات بنجاح');
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في تصدير البيانات: ' + error.toString());
        })
        .exportCustomers();
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount || 0);
}

function formatDate(dateString) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showSuccess(message) {
    window.ERP.showSuccess(message);
}

function showError(message) {
    window.ERP.showError(message);
}
</script>
