<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= config.SYSTEM_NAME ?></title>
    
    <!-- External CDN Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <?= include('styles') ?>
</head>
<body class="bg-gray-50">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 bg-blue-600 flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold mb-2"><?= config.SYSTEM_NAME ?></h2>
            <p class="text-lg">جاري تحميل النظام...</p>
            <div class="mt-4">
                <div class="bg-white bg-opacity-20 rounded-full h-2 w-64 mx-auto">
                    <div id="loadingProgress" class="bg-white h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="appContainer" class="hidden">
        <!-- Top Navigation -->
        <nav class="bg-blue-600 text-white shadow-lg">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center py-3">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <i class="fas fa-chart-line text-2xl"></i>
                        <h1 class="text-xl font-bold"><?= config.SYSTEM_NAME ?></h1>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button id="refreshBtn" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <span id="userName">المستخدم</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="fas fa-user-cog"></i> الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog"></i> الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <div class="flex">
            <aside id="sidebar" class="bg-gray-800 text-white w-64 min-h-screen transition-all duration-300">
                <div class="p-4">
                    <button id="sidebarToggle" class="text-white hover:text-gray-300 mb-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    
                    <!-- Dashboard Link -->
                    <div class="mb-6">
                        <button onclick="goToDashboard()" class="w-full text-right p-3 rounded-lg bg-blue-600 hover:bg-blue-700 transition-colors">
                            <i class="fas fa-tachometer-alt ml-2"></i>
                            <span class="sidebar-text">لوحة التحكم</span>
                        </button>
                    </div>

                    <!-- Modules Menu -->
                    <nav class="space-y-2">
                        <div class="module-group">
                            <h3 class="text-gray-400 text-sm font-semibold mb-2 sidebar-text">إدارة العملاء والمبيعات</h3>
                            <button onclick="openModule('CRM')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-users ml-2"></i>
                                <span class="sidebar-text">إدارة العملاء (CRM)</span>
                            </button>
                            <button onclick="openModule('Sales')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-shopping-cart ml-2"></i>
                                <span class="sidebar-text">المبيعات ونقاط البيع</span>
                            </button>
                            <button onclick="openModule('VanDistribution')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-truck ml-2"></i>
                                <span class="sidebar-text">توزيع البضائع</span>
                            </button>
                        </div>

                        <div class="module-group">
                            <h3 class="text-gray-400 text-sm font-semibold mb-2 sidebar-text">إدارة المخزون والمشتريات</h3>
                            <button onclick="openModule('Products')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-box ml-2"></i>
                                <span class="sidebar-text">المنتجات</span>
                            </button>
                            <button onclick="openModule('Inventory')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-warehouse ml-2"></i>
                                <span class="sidebar-text">المخازن</span>
                            </button>
                            <button onclick="openModule('Purchases')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-shopping-bag ml-2"></i>
                                <span class="sidebar-text">المشتريات</span>
                            </button>
                            <button onclick="openModule('Manufacturing')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-industry ml-2"></i>
                                <span class="sidebar-text">التصنيع</span>
                            </button>
                        </div>

                        <div class="module-group">
                            <h3 class="text-gray-400 text-sm font-semibold mb-2 sidebar-text">الإدارة المالية</h3>
                            <button onclick="openModule('Treasury')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-coins ml-2"></i>
                                <span class="sidebar-text">الخزنة</span>
                            </button>
                            <button onclick="openModule('Wallets')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-credit-card ml-2"></i>
                                <span class="sidebar-text">الفيزا والمحافظ</span>
                            </button>
                            <button onclick="openModule('Accounting')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-calculator ml-2"></i>
                                <span class="sidebar-text">الحسابات</span>
                            </button>
                        </div>

                        <div class="module-group">
                            <h3 class="text-gray-400 text-sm font-semibold mb-2 sidebar-text">الإعدادات</h3>
                            <button onclick="openModule('Settings')" class="module-btn w-full text-right p-3 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-cog ml-2"></i>
                                <span class="sidebar-text">الإعدادات العامة</span>
                            </button>
                        </div>
                    </nav>
                </div>
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1 p-6">
                <div id="contentArea">
                    <!-- Dashboard will be loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Scripts -->
    <?= include('scripts') ?>
</body>
</html>
