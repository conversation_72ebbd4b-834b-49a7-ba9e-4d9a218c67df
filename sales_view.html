<div class="animate-fade-in">
    <!-- Sales Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-shopping-cart text-primary ml-2"></i>
                المبيعات ونقاط البيع
            </h2>
            <p class="text-gray-600">إدارة الفواتير والمبيعات ونقاط البيع</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="goToDashboard()">
                <i class="fas fa-arrow-right ml-1"></i> العودة
            </button>
            <button class="btn btn-success" onclick="showNewInvoiceModal()">
                <i class="fas fa-plus ml-1"></i> فاتورة جديدة
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-invoice text-4xl mb-3"></i>
                    <h3 id="totalInvoices" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card success">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day text-4xl mb-3"></i>
                    <h3 id="todayInvoices" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">فواتير اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card warning">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave text-4xl mb-3"></i>
                    <h3 id="totalSales" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">إجمالي المبيعات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card danger">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-4xl mb-3"></i>
                    <h3 id="remainingAmount" class="text-2xl font-bold">0</h3>
                    <p class="mb-0">المبالغ المستحقة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="البحث في الفواتير...">
                        <button class="btn btn-primary" onclick="searchInvoices()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select id="statusFilter" class="form-select" onchange="filterInvoices()">
                        <option value="">جميع الحالات</option>
                        <option value="مؤكدة">مؤكدة</option>
                        <option value="مسودة">مسودة</option>
                        <option value="ملغية">ملغية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select id="paymentStatusFilter" class="form-select" onchange="filterInvoices()">
                        <option value="">حالة الدفع</option>
                        <option value="مدفوعة">مدفوعة</option>
                        <option value="مدفوعة جزئياً">مدفوعة جزئياً</option>
                        <option value="غير مدفوعة">غير مدفوعة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" id="dateFromFilter" class="form-control" onchange="filterInvoices()">
                </div>
                <div class="col-md-2">
                    <input type="date" id="dateToFilter" class="form-control" onchange="filterInvoices()">
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list ml-2"></i>
                قائمة الفواتير
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success btn-sm" onclick="exportInvoices()">
                    <i class="fas fa-download ml-1"></i> تصدير
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshInvoices()">
                    <i class="fas fa-sync-alt ml-1"></i> تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="invoicesTableContainer">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الفواتير...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Invoice Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice ml-2"></i>
                    فاتورة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="invoiceForm">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="customerId" class="form-label">العميل *</label>
                            <select class="form-select" id="customerId" required>
                                <option value="">اختر العميل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="saleDate" class="form-label">تاريخ البيع</label>
                            <input type="date" class="form-control" id="saleDate">
                        </div>
                        <div class="col-md-3">
                            <label for="dueDate" class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" class="form-control" id="dueDate">
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="salesRepresentative" class="form-label">المندوب</label>
                            <input type="text" class="form-control" id="salesRepresentative">
                        </div>
                        <div class="col-md-3">
                            <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="paymentMethod">
                                <option value="نقدي">نقدي</option>
                                <option value="آجل">آجل</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="invoiceStatus" class="form-label">حالة الفاتورة</label>
                            <select class="form-select" id="invoiceStatus">
                                <option value="مؤكدة">مؤكدة</option>
                                <option value="مسودة">مسودة</option>
                            </select>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">عناصر الفاتورة</h6>
                            <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceItem()">
                                <i class="fas fa-plus ml-1"></i> إضافة عنصر
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم %</th>
                                        <th>الضريبة %</th>
                                        <th>الإجمالي</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItemsTable">
                                    <!-- Items will be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="row">
                        <div class="col-md-8">
                            <label for="invoiceNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="invoiceNotes" rows="3"></textarea>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <table class="table table-sm mb-0">
                                        <tr>
                                            <td>المجموع الفرعي:</td>
                                            <td class="text-end" id="subtotalAmount">0.00</td>
                                        </tr>
                                        <tr>
                                            <td>الخصم:</td>
                                            <td class="text-end" id="discountAmount">0.00</td>
                                        </tr>
                                        <tr>
                                            <td>الضريبة:</td>
                                            <td class="text-end" id="taxAmount">0.00</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>الإجمالي:</strong></td>
                                            <td class="text-end"><strong id="totalAmount">0.00</strong></td>
                                        </tr>
                                        <tr>
                                            <td>المبلغ المدفوع:</td>
                                            <td class="text-end">
                                                <input type="number" class="form-control form-control-sm" id="paidAmount" min="0" step="0.01" value="0" onchange="updateRemainingAmount()">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>المتبقي:</td>
                                            <td class="text-end" id="remainingAmountDisplay">0.00</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveInvoice()">
                    <i class="fas fa-save ml-1"></i> حفظ الفاتورة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Sales Module JavaScript
let invoiceItems = [];
let itemCounter = 0;

window.moduleInit = function() {
    loadSalesStats();
    loadInvoices();
    loadCustomers();
    setDefaultDates();

    // Setup search on Enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchInvoices();
        }
    });
};

// Set default dates
function setDefaultDates() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('saleDate').value = today;

    // Set due date to 30 days from today
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);
    document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
}

// Load sales statistics
function loadSalesStats() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                const stats = result.data;
                document.getElementById('totalInvoices').textContent = stats.totalInvoices;
                document.getElementById('todayInvoices').textContent = stats.todayInvoices;
                document.getElementById('totalSales').textContent = formatCurrency(stats.totalSales);
                document.getElementById('remainingAmount').textContent = formatCurrency(stats.remainingAmount);
            }
        })
        .withFailureHandler(function(error) {
            console.error('Error loading sales stats:', error);
        })
        .getSalesStats();
}

// Load invoices
function loadInvoices() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                renderInvoicesTable(result.data);
            } else {
                showError('فشل في تحميل الفواتير: ' + result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في تحميل الفواتير: ' + error.toString());
        })
        .getAllInvoices();
}

// Load customers for dropdown
function loadCustomers() {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                const customerSelect = document.getElementById('customerId');
                customerSelect.innerHTML = '<option value="">اختر العميل</option>';

                result.data.forEach(customer => {
                    if (customer.isActive) {
                        const option = document.createElement('option');
                        option.value = customer.id;
                        option.textContent = `${customer.name} - ${customer.customerCode || ''}`;
                        option.dataset.customerName = customer.name;
                        customerSelect.appendChild(option);
                    }
                });
            }
        })
        .withFailureHandler(function(error) {
            console.error('Error loading customers:', error);
        })
        .getAllCustomers({ isActive: true });
}

// Render invoices table
function renderInvoicesTable(invoices) {
    const container = document.getElementById('invoicesTableContainer');

    if (invoices.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-file-invoice text-6xl text-gray-400 mb-4"></i>
                <h4 class="text-gray-600">لا توجد فواتير</h4>
                <p class="text-gray-500">ابدأ بإنشاء فاتورة جديدة</p>
                <button class="btn btn-primary" onclick="showNewInvoiceModal()">
                    <i class="fas fa-plus ml-1"></i> فاتورة جديدة
                </button>
            </div>
        `;
        return;
    }

    const tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>التاريخ</th>
                        <th>الإجمالي</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>حالة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoices.map(invoice => `
                        <tr>
                            <td><strong>${invoice.invoiceNumber || ''}</strong></td>
                            <td>${invoice.customerName || ''}</td>
                            <td>${formatDate(invoice.saleDate)}</td>
                            <td>${formatCurrency(invoice.totalAmount || 0)}</td>
                            <td>${formatCurrency(invoice.paidAmount || 0)}</td>
                            <td>${formatCurrency(invoice.remainingAmount || 0)}</td>
                            <td>
                                <span class="badge ${getPaymentStatusClass(invoice.paymentStatus)}">
                                    ${invoice.paymentStatus || 'غير محدد'}
                                </span>
                            </td>
                            <td>
                                <span class="badge ${getInvoiceStatusClass(invoice.invoiceStatus)}">
                                    ${invoice.invoiceStatus || 'غير محدد'}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewInvoice('${invoice.id}')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="editInvoice('${invoice.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="printInvoice('${invoice.id}')" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteInvoiceConfirm('${invoice.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHTML;
}

// Show new invoice modal
function showNewInvoiceModal() {
    document.getElementById('invoiceForm').reset();
    invoiceItems = [];
    itemCounter = 0;
    setDefaultDates();
    updateInvoiceItemsTable();
    updateInvoiceTotals();

    const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
    modal.show();
}

// Add invoice item
function addInvoiceItem() {
    const item = {
        id: ++itemCounter,
        productId: '',
        productName: '',
        productCode: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 15
    };

    invoiceItems.push(item);
    updateInvoiceItemsTable();
}

// Update invoice items table
function updateInvoiceItemsTable() {
    const tbody = document.getElementById('invoiceItemsTable');

    if (invoiceItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-3">
                    لا توجد عناصر. اضغط "إضافة عنصر" لبدء إضافة المنتجات.
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = invoiceItems.map(item => `
        <tr>
            <td>
                <input type="text" class="form-control form-control-sm"
                       value="${item.productName}"
                       onchange="updateItemField(${item.id}, 'productName', this.value)"
                       placeholder="اسم المنتج">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm"
                       value="${item.quantity}" min="1" step="1"
                       onchange="updateItemField(${item.id}, 'quantity', parseFloat(this.value) || 1)">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm"
                       value="${item.unitPrice}" min="0" step="0.01"
                       onchange="updateItemField(${item.id}, 'unitPrice', parseFloat(this.value) || 0)">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm"
                       value="${item.discount}" min="0" max="100" step="0.01"
                       onchange="updateItemField(${item.id}, 'discount', parseFloat(this.value) || 0)">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm"
                       value="${item.taxRate}" min="0" max="100" step="0.01"
                       onchange="updateItemField(${item.id}, 'taxRate', parseFloat(this.value) || 0)">
            </td>
            <td class="text-end">
                <strong>${formatCurrency(calculateLineTotal(item))}</strong>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger"
                        onclick="removeInvoiceItem(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Update item field
function updateItemField(itemId, field, value) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (item) {
        item[field] = value;
        updateInvoiceItemsTable();
        updateInvoiceTotals();
    }
}

// Remove invoice item
function removeInvoiceItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceItemsTable();
    updateInvoiceTotals();
}

// Calculate line total
function calculateLineTotal(item) {
    const baseAmount = item.quantity * item.unitPrice;
    const discountAmount = baseAmount * (item.discount || 0) / 100;
    const afterDiscount = baseAmount - discountAmount;
    const taxAmount = afterDiscount * (item.taxRate || 0) / 100;
    return afterDiscount + taxAmount;
}

// Update invoice totals
function updateInvoiceTotals() {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    invoiceItems.forEach(item => {
        const baseAmount = item.quantity * item.unitPrice;
        subtotal += baseAmount;
        totalDiscount += baseAmount * (item.discount || 0) / 100;
        totalTax += (baseAmount - (baseAmount * (item.discount || 0) / 100)) * (item.taxRate || 0) / 100;
    });

    const total = subtotal - totalDiscount + totalTax;

    document.getElementById('subtotalAmount').textContent = formatCurrency(subtotal);
    document.getElementById('discountAmount').textContent = formatCurrency(totalDiscount);
    document.getElementById('taxAmount').textContent = formatCurrency(totalTax);
    document.getElementById('totalAmount').textContent = formatCurrency(total);

    updateRemainingAmount();
}

// Update remaining amount
function updateRemainingAmount() {
    const totalAmount = parseFloat(document.getElementById('totalAmount').textContent.replace(/[^\d.-]/g, '')) || 0;
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const remaining = totalAmount - paidAmount;

    document.getElementById('remainingAmountDisplay').textContent = formatCurrency(remaining);
}

// Save invoice
function saveInvoice() {
    if (invoiceItems.length === 0) {
        showError('يجب إضافة عنصر واحد على الأقل');
        return;
    }

    const customerId = document.getElementById('customerId').value;
    if (!customerId) {
        showError('يجب اختيار العميل');
        return;
    }

    const customerSelect = document.getElementById('customerId');
    const selectedOption = customerSelect.options[customerSelect.selectedIndex];
    const customerName = selectedOption.dataset.customerName;

    const invoiceData = {
        customerId: customerId,
        customerName: customerName,
        saleDate: document.getElementById('saleDate').value,
        dueDate: document.getElementById('dueDate').value,
        salesRepresentative: document.getElementById('salesRepresentative').value,
        paymentMethod: document.getElementById('paymentMethod').value,
        invoiceStatus: document.getElementById('invoiceStatus').value,
        paidAmount: parseFloat(document.getElementById('paidAmount').value) || 0,
        notes: document.getElementById('invoiceNotes').value,
        items: invoiceItems.map(item => ({
            productId: item.productId || 'MANUAL_' + Date.now(),
            productName: item.productName,
            productCode: item.productCode,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount,
            taxRate: item.taxRate
        }))
    };

    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                showSuccess(result.message);
                bootstrap.Modal.getInstance(document.getElementById('invoiceModal')).hide();
                loadInvoices();
                loadSalesStats();
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في حفظ الفاتورة: ' + error.toString());
        })
        .createInvoice(invoiceData);
}

// Search invoices
function searchInvoices() {
    const searchTerm = document.getElementById('searchInput').value;

    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                renderInvoicesTable(result.data);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في البحث: ' + error.toString());
        })
        .searchInvoices(searchTerm);
}

// Filter invoices
function filterInvoices() {
    const statusFilter = document.getElementById('statusFilter').value;
    const paymentStatusFilter = document.getElementById('paymentStatusFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;

    const filters = {};
    if (statusFilter) filters.invoiceStatus = statusFilter;
    if (paymentStatusFilter) filters.paymentStatus = paymentStatusFilter;

    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                let filteredData = result.data;

                // Apply date filters
                if (dateFrom || dateTo) {
                    filteredData = filteredData.filter(invoice => {
                        const invoiceDate = new Date(invoice.saleDate);
                        const fromDate = dateFrom ? new Date(dateFrom) : null;
                        const toDate = dateTo ? new Date(dateTo) : null;

                        if (fromDate && invoiceDate < fromDate) return false;
                        if (toDate && invoiceDate > toDate) return false;
                        return true;
                    });
                }

                renderInvoicesTable(filteredData);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في التصفية: ' + error.toString());
        })
        .getAllInvoices(filters);
}

// Refresh invoices
function refreshInvoices() {
    loadInvoices();
    loadSalesStats();
}

// View invoice
function viewInvoice(invoiceId) {
    google.script.run
        .withSuccessHandler(function(result) {
            if (result.success) {
                showInvoiceDetails(result.data);
            } else {
                showError(result.message);
            }
        })
        .withFailureHandler(function(error) {
            showError('خطأ في جلب تفاصيل الفاتورة: ' + error.toString());
        })
        .getInvoiceById(invoiceId);
}

// Delete invoice confirmation
function deleteInvoiceConfirm(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        google.script.run
            .withSuccessHandler(function(result) {
                if (result.success) {
                    showSuccess(result.message);
                    loadInvoices();
                    loadSalesStats();
                } else {
                    showError(result.message);
                }
            })
            .withFailureHandler(function(error) {
                showError('خطأ في حذف الفاتورة: ' + error.toString());
            })
            .deleteInvoice(invoiceId);
    }
}

// Print invoice
function printInvoice(invoiceId) {
    showSuccess('وظيفة الطباعة قيد التطوير');
}

// Edit invoice
function editInvoice(invoiceId) {
    showSuccess('وظيفة التعديل قيد التطوير');
}

// Export invoices
function exportInvoices() {
    showSuccess('وظيفة التصدير قيد التطوير');
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount || 0);
}

function formatDate(dateString) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function getPaymentStatusClass(status) {
    switch (status) {
        case 'مدفوعة': return 'bg-success';
        case 'مدفوعة جزئياً': return 'bg-warning';
        case 'غير مدفوعة': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getInvoiceStatusClass(status) {
    switch (status) {
        case 'مؤكدة': return 'bg-success';
        case 'مسودة': return 'bg-warning';
        case 'ملغية': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function showSuccess(message) {
    window.ERP.showSuccess(message);
}

function showError(message) {
    window.ERP.showError(message);
}

// Show invoice details function
function showInvoiceDetails(data) {
    const invoice = data.invoice;
    const items = data.items;

    // Create and show invoice details modal
    const modalHTML = `
        <div class="modal fade" id="invoiceDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-invoice ml-2"></i>
                            تفاصيل الفاتورة ${invoice.invoiceNumber}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6 class="text-primary">معلومات الفاتورة</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>رقم الفاتورة:</strong></td><td>${invoice.invoiceNumber}</td></tr>
                                    <tr><td><strong>العميل:</strong></td><td>${invoice.customerName}</td></tr>
                                    <tr><td><strong>تاريخ البيع:</strong></td><td>${formatDate(invoice.saleDate)}</td></tr>
                                    <tr><td><strong>تاريخ الاستحقاق:</strong></td><td>${formatDate(invoice.dueDate)}</td></tr>
                                    <tr><td><strong>المندوب:</strong></td><td>${invoice.salesRepresentative || ''}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">معلومات الدفع</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>طريقة الدفع:</strong></td><td>${invoice.paymentMethod}</td></tr>
                                    <tr><td><strong>حالة الدفع:</strong></td><td><span class="badge ${getPaymentStatusClass(invoice.paymentStatus)}">${invoice.paymentStatus}</span></td></tr>
                                    <tr><td><strong>حالة الفاتورة:</strong></td><td><span class="badge ${getInvoiceStatusClass(invoice.invoiceStatus)}">${invoice.invoiceStatus}</span></td></tr>
                                    <tr><td><strong>المبلغ المدفوع:</strong></td><td>${formatCurrency(invoice.paidAmount)}</td></tr>
                                    <tr><td><strong>المتبقي:</strong></td><td>${formatCurrency(invoice.remainingAmount)}</td></tr>
                                </table>
                            </div>
                        </div>

                        <h6 class="text-primary">عناصر الفاتورة</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الخصم</th>
                                        <th>الضريبة</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${items.map(item => `
                                        <tr>
                                            <td>${item.productName}</td>
                                            <td>${item.quantity}</td>
                                            <td>${formatCurrency(item.unitPrice)}</td>
                                            <td>${item.discount}%</td>
                                            <td>${item.taxRate}%</td>
                                            <td>${formatCurrency(item.lineTotal)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-8">
                                ${invoice.notes ? `<p><strong>ملاحظات:</strong><br>${invoice.notes}</p>` : ''}
                            </div>
                            <div class="col-md-4">
                                <table class="table table-sm">
                                    <tr><td>المجموع الفرعي:</td><td class="text-end">${formatCurrency(invoice.subtotal)}</td></tr>
                                    <tr><td>الخصم:</td><td class="text-end">${formatCurrency(invoice.discountAmount)}</td></tr>
                                    <tr><td>الضريبة:</td><td class="text-end">${formatCurrency(invoice.taxAmount)}</td></tr>
                                    <tr class="table-primary"><td><strong>الإجمالي:</strong></td><td class="text-end"><strong>${formatCurrency(invoice.totalAmount)}</strong></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="printInvoice('${invoice.id}')">
                            <i class="fas fa-print ml-1"></i> طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('invoiceDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body and show
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('invoiceDetailsModal'));
    modal.show();

    // Remove modal from DOM when hidden
    document.getElementById('invoiceDetailsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}
</script>
